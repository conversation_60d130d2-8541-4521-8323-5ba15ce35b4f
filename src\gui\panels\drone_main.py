#!/usr/bin/env python3
"""
RTMP Simple Reader
Chỉ đơn giản lấy ảnh từ RTMP stream
"""

import cv2
import argparse
import time
from datetime import datetime
from pyzbar import pyzbar
import numpy as np
from ultralytics import YOLO
import threading
import queue
import os
import sys

class RTMPReader:
    def __init__(self, rtmp_url, enable_barcode=False, enable_yolo=False):
        self.rtmp_url = rtmp_url
        self.cap = None
        self.running = False
        self.barcode_history = []
        self.enable_barcode = enable_barcode
        self.enable_yolo = enable_yolo
        self.frame_skip_counter = 0
        self.tracked_barcodes = {}  # Lưu trữ barcode đang được track
        self.next_track_id = 1
        
        # YOLO model và threading
        self.yolo_model = None
        self.yolo_queue = queue.Queue(maxsize=1)  # Chỉ 1 frame để giảm lag
        self.yolo_result_queue = queue.Queue(maxsize=1)
        self.yolo_thread = None
        self.yolo_running = False
        self.last_yolo_frame = None
        self.yolo_skip_frames = 0
        
        if enable_yolo:
            self.init_yolo()

    def init_yolo(self):
        """Khởi tạo YOLO model trong thread riêng"""
        print("🤖 Đang tải YOLO model...")
        try:
            self.yolo_model = YOLO("model\last.pt")
            # Tối ưu YOLO settings
            self.yolo_model.overrides['verbose'] = False
            self.yolo_model.overrides['save'] = False
            self.yolo_model.overrides['show'] = False
            
            self.yolo_running = True
            self.yolo_thread = threading.Thread(target=self.yolo_worker, daemon=True)
            self.yolo_thread.start()
            print("✅ YOLO model đã sẵn sàng")
        except Exception as e:
            print(f"❌ Lỗi khi tải YOLO: {e}")
            self.enable_yolo = False

    def yolo_worker(self):
        """Worker thread xử lý YOLO"""
        while self.yolo_running:
            try:
                # Lấy frame từ queue (timeout ngắn)
                frame = self.yolo_queue.get(timeout=0.05)
                
                # Resize frame để tăng tốc
                h, w = frame.shape[:2]
                if w > 640:
                    scale = 640 / w
                    new_w, new_h = int(w * scale), int(h * scale)
                    frame_small = cv2.resize(frame, (new_w, new_h))
                else:
                    frame_small = frame
                    scale = 1.0
                
                # Xử lý YOLO với frame nhỏ hơn (bỏ half=True vì CPU không hỗ trợ)
                results = self.yolo_model(frame_small, conf=0.25, imgsz=320)[0]  # Giảm size xuống 320
                
                # Tạo frame kết quả với size gốc
                result_frame = frame.copy()
                if results.boxes is not None and len(results.boxes) > 0:
                    for box in results.boxes:
                        # Lấy thông tin detection
                        conf = float(box.conf[0].cpu().numpy())
                        cls = int(box.cls[0].cpu().numpy())
                        
                        # Scale lại tọa độ về size gốc
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        if scale != 1.0:
                            x1, y1, x2, y2 = x1/scale, y1/scale, x2/scale, y2/scale
                        x1, y1, x2, y2 = map(int, [x1, y1, x2, y2])
                        
                        # Đảm bảo tọa độ hợp lệ
                        h, w = result_frame.shape[:2]
                        x1, y1 = max(0, x1), max(0, y1)
                        x2, y2 = min(w, x2), min(h, y2)
                        
                        # Crop vùng detect để đọc barcode
                        barcode_text = ""
                        if y2 > y1 and x2 > x1:  # Đảm bảo có vùng hợp lệ
                            cropped = result_frame[y1:y2, x1:x2]
                            if cropped.size > 0:
                                # Đọc barcode trong vùng crop
                                barcodes_in_crop = self.read_barcode_in_crop(cropped, (x1, y1))
                                if barcodes_in_crop:
                                    barcode_data = barcodes_in_crop[0]['data'][:20]  # Lấy barcode đầu tiên
                                    barcode_text = f" | CODE: {barcode_data}"
                                    print(f"🔍 {datetime.now().strftime('%H:%M:%S')} Barcode trong {self.yolo_model.names[cls]}: {barcode_data}")
                                    
                                    # Lưu ảnh crop với thông tin barcode
                                    self.save_img_barcode_crop(cropped, barcode_data, self.yolo_model.names[cls])
                        
                        # Vẽ bounding box (màu khác nếu có barcode)
                        color = (0, 255, 255) if barcode_text else (0, 255, 0)  # Vàng nếu có barcode, xanh nếu không
                        cv2.rectangle(result_frame, (x1, y1), (x2, y2), color, 2)

                        # Vẽ label với thông tin barcode
                        label = f"{self.yolo_model.names[cls]}: {conf:.2f}{barcode_text}"
                        
                        # Background cho text
                        text_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                        cv2.rectangle(result_frame, (x1, y1 - text_size[1] - 10), 
                                    (x1 + text_size[0], y1), color, -1)
                        
                        # Text trắng
                        cv2.putText(result_frame, label, (x1, y1-5), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0,255,0), 2) 
                
                # Clear old results và đưa kết quả mới vào
                try:
                    self.yolo_result_queue.get_nowait()  # Xóa kết quả cũ
                except queue.Empty:
                    pass
                self.yolo_result_queue.put(result_frame)
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Lỗi YOLO worker: {e}")
                continue

    def process_yolo_async(self, frame):
        """Xử lý YOLO không đồng bộ với frame skipping"""
        if not self.enable_yolo or not self.yolo_running:
            return frame
        
        # Skip frames để giảm tải (chỉ xử lý mỗi 3 frame)
        self.yolo_skip_frames += 1
        if self.yolo_skip_frames % 3 == 0:
            # Clear queue cũ và đưa frame mới vào
            try:
                self.yolo_queue.get_nowait()  # Xóa frame cũ
            except queue.Empty:
                pass
            
            if not self.yolo_queue.full():
                self.yolo_queue.put(frame.copy())
        
        # Lấy kết quả nếu có (non-blocking)
        try:
            processed_frame = self.yolo_result_queue.get_nowait()
            self.last_yolo_frame = processed_frame
            return processed_frame
        except queue.Empty:
            # Trả về kết quả cũ nhất nếu có, nếu không thì frame gốc
            return self.last_yolo_frame if self.last_yolo_frame is not None else frame
    
    def read_barcode_in_crop(self, cropped_image, offset=(0, 0)):
        """Đọc barcode trong vùng crop từ YOLO detection"""
        barcodes = []
        
        try:
            # Chuyển sang grayscale
            if len(cropped_image.shape) == 3:
                gray = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2GRAY)
            else:
                gray = cropped_image
            
            # Cải thiện chất lượng ảnh cho barcode
            # Tăng độ tương phản
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            
            # Thử nhiều kỹ thuật cải thiện
            enhanced_images = [
                enhanced,
                cv2.GaussianBlur(gray, (3, 3), 0),
                cv2.medianBlur(gray, 3),
            ]
            
            # Thử adaptive threshold
            try:
                adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                               cv2.THRESH_BINARY, 11, 2)
                enhanced_images.append(adaptive)
            except:
                pass
            
            # Thử decode với từng phiên bản ảnh
            for img in enhanced_images:
                try:
                    detected_barcodes = pyzbar.decode(img)
                    
                    for barcode in detected_barcodes:
                        try:
                            # Decode dữ liệu barcode
                            barcode_data = barcode.data.decode('utf-8')

                            # config các kiểu barcode
                            barcode_type = {
                                'CODE128': 'CODE128',
                                'EAN13': 'EAN13',
                                'EAN8': 'EAN8',
                                'UPC_A': 'UPC_A',
                                'UPC_E': 'UPC_E',
                            }
                            barcode_type = barcode_type[barcode.type]
                            
                            # Lấy vị trí barcode (điều chỉnh với offset)
                            (x, y, w, h) = barcode.rect
                            x += offset[0]
                            y += offset[1]
                            
                            # Thông tin barcode
                            barcode_info = {
                                'data': barcode_data,
                                'type': barcode_type,
                                'position': (x, y, w, h),
                                'timestamp': datetime.now().strftime("%H:%M:%S")
                            }
                            barcodes.append(barcode_info)
                            
                            # Lưu vào history
                            if barcode_data not in [b['data'] for b in self.barcode_history[-5:]]:
                                self.barcode_history.append(barcode_info)
                                self.save_barcode_to_file(barcode_info)
                                # Lưu ảnh crop từ YOLO detection
                                # self.save_img_barcode_crop(img, barcode_data, "yolo_crop")
                                
                            
                        except Exception as e:
                            continue
                            
                except Exception as e:
                    continue
        
        except Exception as e:
            print(f"❌ Lỗi khi đọc barcode trong crop: {e}")
        
        return barcodes
    
    def connect(self):
        """Kết nối tới RTMP stream"""
        print(f"📡 Đang kết nối tới: {self.rtmp_url}")
        
        try:
            self.cap = cv2.VideoCapture(self.rtmp_url, cv2.CAP_FFMPEG)
            
            # Giảm buffer để giảm delay
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            # Tối ưu thêm cho RTMP
            self.cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('H', '2', '6', '4'))
            
            if not self.cap.isOpened():
                print(f"❌ Không thể kết nối tới RTMP stream")
                return False
            
            # Lấy thông tin stream
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = self.cap.get(cv2.CAP_PROP_FPS)
            
            print(f"✅ Kết nối thành công!")
            print(f"📺 Kích thước: {width}x{height}")
            print(f"🎯 FPS: {fps}")
            
            return True
            
        except Exception as e:
            print(f"❌ Lỗi kết nối: {e}")
            return False
    
    def calculate_distance(self, pos1, pos2):
        """Tính khoảng cách giữa 2 vị trí"""
        x1, y1, w1, h1 = pos1
        x2, y2, w2, h2 = pos2
        center1 = (x1 + w1//2, y1 + h1//2)
        center2 = (x2 + w2//2, y2 + h2//2)
        return np.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)
    
    def update_tracking(self, current_barcodes):
        """Cập nhật tracking cho các barcode"""
        # Xóa các track cũ (không xuất hiện trong 10 frame)
        to_remove = []
        for track_id, track_info in self.tracked_barcodes.items():
            track_info['frames_missing'] += 1
            if track_info['frames_missing'] > 10:
                to_remove.append(track_id)
        
        for track_id in to_remove:
            del self.tracked_barcodes[track_id]
            print(f"🔄 Đã mất track ID {track_id}")
        
        # Matching với các barcode hiện tại
        for barcode_info in current_barcodes:
            best_match_id = None
            best_distance = float('inf')
            
            # Tìm track gần nhất với cùng data
            for track_id, track_info in self.tracked_barcodes.items():
                if track_info['data'] == barcode_info['data']:
                    distance = self.calculate_distance(track_info['last_position'], barcode_info['position'])
                    if distance < 100 and distance < best_distance:  # Ngưỡng 100 pixel
                        best_distance = distance
                        best_match_id = track_id
            
            if best_match_id:
                # Cập nhật track hiện có
                track = self.tracked_barcodes[best_match_id]
                old_pos = track['last_position']
                new_pos = barcode_info['position']
                
                # Tính vận tốc di chuyển
                velocity_x = (new_pos[0] + new_pos[2]//2) - (old_pos[0] + old_pos[2]//2)
                velocity_y = (new_pos[1] + new_pos[3]//2) - (old_pos[1] + old_pos[3]//2)
                
                track['last_position'] = new_pos
                track['frames_missing'] = 0
                track['frame_count'] += 1
                track['velocity'] = (velocity_x, velocity_y)
                track['path'].append((new_pos[0] + new_pos[2]//2, new_pos[1] + new_pos[3]//2))
                
                # Giới hạn path history
                if len(track['path']) > 30:
                    track['path'] = track['path'][-30:]
                
                barcode_info['track_id'] = best_match_id
                
            else:
                # Tạo track mới
                track_id = self.next_track_id
                self.next_track_id += 1
                
                center = (barcode_info['position'][0] + barcode_info['position'][2]//2,
                         barcode_info['position'][1] + barcode_info['position'][3]//2)
                
                self.tracked_barcodes[track_id] = {
                    'data': barcode_info['data'],
                    'type': barcode_info['type'],
                    'last_position': barcode_info['position'],
                    'frames_missing': 0,
                    'frame_count': 1,
                    'velocity': (0, 0),
                    'path': [center],
                    'created_time': datetime.now()
                }
                
                barcode_info['track_id'] = track_id
                print(f"🆕 Tạo track mới ID {track_id} cho {barcode_info['data']}")
    
    def detect_barcode(self, frame):
        """Phát hiện và tracking barcode/QR code"""
        barcodes = []
        
        try:
            # Chuyển sang grayscale
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Cải thiện chất lượng ảnh
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            
            # Phát hiện barcode
            detected_barcodes = pyzbar.decode(enhanced)
            
            for barcode in detected_barcodes:
                try:
                    # Decode dữ liệu barcode
                    barcode_data = barcode.data.decode('utf-8')
                    barcode_type = barcode.type
                    
                    # Lấy vị trí barcode
                    (x, y, w, h) = barcode.rect
                    
                    # Thông tin barcode
                    barcode_info = {
                        'data': barcode_data,
                        'type': barcode_type,
                        'position': (x, y, w, h),
                        'timestamp': datetime.now().strftime("%H:%M:%S")
                    }
                    barcodes.append(barcode_info)
                
                except Exception as e:
                    print(f"❌ Lỗi decode barcode: {e}")
                    continue
            
            # Cập nhật tracking
            self.update_tracking(barcodes)
            
            # Vẽ tracking information
            for barcode_info in barcodes:
                x, y, w, h = barcode_info['position']
                track_id = barcode_info.get('track_id', 0)
                
                # Màu sắc theo track ID
                # colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), 
                #          (255, 0, 255), (0, 255, 255), (128, 255, 0), (255, 128, 0)]

                colors = [(0, 0, 255), (0, 0, 255), (0, 0, 255), (0, 0, 255), 
                         (0, 0, 255), (0, 0, 255), (0, 0, 255), (0, 0, 255)] # Vàng, đỏ, xanh, vàng, đỏ, xanh, xanh lá, cam
                color = colors[track_id % len(colors)]
                
                # Vẽ khung barcode
                cv2.rectangle(frame, (x, y), (x + w, y + h), color, 3)
                
                # Vẽ tracking path nếu có
                if track_id in self.tracked_barcodes:
                    track = self.tracked_barcodes[track_id]
                    path = track['path']
                    
                    # Vẽ đường đi
                    if len(path) > 1:
                        for i in range(1, len(path)):
                            cv2.line(frame, path[i-1], path[i], color, 2)
                    
                    # Vẽ điểm hiện tại
                    if path:
                        cv2.circle(frame, path[-1], 5, color, -1)
                    
                    # Hiển thị thông tin tracking
                    velocity_x, velocity_y = track['velocity']
                    speed = np.sqrt(velocity_x**2 + velocity_y**2)
                    
                    # Text với thông tin tracking
                    track_text = f"ID:{track_id} | {barcode_info['type']}: {barcode_info['data']}"
                    if len(track_text) > 50:
                        track_text = track_text[:50] + "..."
                    
                    speed_text = f"Speed: {speed:.1f}px/frame | Frames: {track['frame_count']}"
                    
                    # Background cho text
                    text_size = cv2.getTextSize(track_text, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                    cv2.rectangle(frame, (x, y - text_size[1] - 35), 
                                (x + max(text_size[0], 300), y), color, -1)
                    
                    # Text trắng
                    cv2.putText(frame, track_text, (x, y - 20), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
                    cv2.putText(frame, speed_text, (x, y - 5), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
                    
                    # Vẽ hướng di chuyển
                    if abs(velocity_x) > 2 or abs(velocity_y) > 2:
                        center = (x + w//2, y + h//2)
                        end_point = (center[0] + velocity_x * 3, center[1] + velocity_y * 3)
                        cv2.arrowedLine(frame, center, end_point, color, 3)
                
                # Lưu vào history (tránh trùng lặp)
                if barcode_info['data'] not in [b['data'] for b in self.barcode_history[-10:]]:
                    self.barcode_history.append(barcode_info)
                    print(f"🔍 Track ID {track_id}: {barcode_info['type']} = {barcode_info['data']}")
                    self.save_barcode_to_file(barcode_info)
                    # Lưu ảnh crop barcode
                    self.save_img_barcode_normal(frame, barcode_info)
        
        except Exception as e:
            print(f"❌ Lỗi khi đọc barcode: {e}")
        
        return frame, barcodes
    
    def save_barcode_to_file(self, barcode_info):
        """Lưu thông tin barcode vào file"""
        try:
            filename = "barcode_history.txt"
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            with open(filename, "a", encoding="utf-8") as f:
                f.write(f"{timestamp} | {barcode_info['type']} | {barcode_info['data']}\n")
                
        except Exception as e:
            print(f"❌ Lỗi khi lưu barcode: {e}")
    
    # Lưu ảnh crop cho barcode detection từ YOLO
    def save_img_barcode_crop(self, cropped_image, barcode_data, object_class="unknown"):
        """Lưu ảnh crop khi detect được barcode"""
        path = "img_barcode"
        if not os.path.exists(path):
            os.makedirs(path)

        try:
            # Tạo tên file với timestamp và thông tin barcode
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # milliseconds
            # Làm sạch barcode data để tránh ký tự đặc biệt trong tên file
            clean_barcode = "".join(c for c in barcode_data if c.isalnum() or c in "-_")[:20]
            filename = f"barcode_{clean_barcode}_{object_class}_{timestamp}.jpg"
            
            filepath = os.path.join(path, filename)
            cv2.imwrite(filepath, cropped_image)
            print(f"📸 Đã lưu ảnh crop barcode: {filename}")
            
        except Exception as e:
            print(f"❌ Lỗi khi lưu ảnh crop barcode: {e}")
    
    # Lưu ảnh crop cho barcode detection thông thường
    def save_img_barcode_normal(self, frame, barcode_info):
        """Lưu ảnh crop cho barcode detection thông thường"""
        path = "img_barcode"
        if not os.path.exists(path):
            os.makedirs(path)

        try:
            # Crop vùng barcode từ frame gốc
            x, y, w, h = barcode_info['position']
            # Mở rộng vùng crop một chút để có context
            margin = 10
            x1 = max(0, x - margin)
            y1 = max(0, y - margin)
            x2 = min(frame.shape[1], x + w + margin)
            y2 = min(frame.shape[0], y + h + margin)
            
            cropped = frame[y1:y2, x1:x2]
            
            if cropped.size > 0:
                # Tạo tên file với timestamp và thông tin barcode
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # milliseconds
                # Làm sạch barcode data để tránh ký tự đặc biệt trong tên file
                clean_barcode = "".join(c for c in barcode_info['data'] if c.isalnum() or c in "-_")[:20]
                filename = f"barcode_{clean_barcode}_normal_{timestamp}.jpg"
                
                filepath = os.path.join(path, filename)
                cv2.imwrite(filepath, cropped)
                print(f"📸 Đã lưu ảnh crop barcode (normal): {filename}")
            
        except Exception as e:
            print(f"❌ Lỗi khi lưu ảnh crop barcode (normal): {e}")

    def run(self):
        """Chạy và hiển thị stream"""
        if not self.connect():
            return False
        
        self.running = True
        frame_count = 0
        start_time = time.time()
        
        print("🎬 Bắt đầu hiển thị stream...")
        controls = ["'q' thoát", "'s' chụp ảnh"]
        if self.enable_barcode:
            controls.extend(["'b' tắt barcode", "'h' lịch sử", "'t' tracking"])
        else:
            controls.append("'b' bật barcode")
        if self.enable_yolo:
            controls.append("'y' tắt YOLO")
        else:
            controls.append("'y' bật YOLO")
        
        print(f"⏹️  Phím tắt: {' | '.join(controls)}")
        
        try:
            while self.running:
                ret, frame = self.cap.read()
                
                if not ret:
                    print("⚠️  Không nhận được frame...")
                    time.sleep(1)
                    continue
                
                # Xử lý YOLO không đồng bộ
                if self.enable_yolo:
                    frame = self.process_yolo_async(frame)
                
                # Phát hiện barcode (chỉ khi được bật và mỗi 10 frame để giảm lag)
                barcodes = []
                if self.enable_barcode and self.frame_skip_counter % 10 == 0:
                    frame, barcodes = self.detect_barcode(frame)
                
                # Tính FPS
                frame_count += 1
                elapsed = time.time() - start_time
                fps = frame_count / elapsed if elapsed > 0 else 0
                
                # Hiển thị thông tin trên frame
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                cv2.putText(frame, f"Time: {timestamp}", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2) # Vàng
                cv2.putText(frame, f"Frame: {frame_count}", (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                cv2.putText(frame, f"FPS: {fps:.1f}", (10, 90), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                
                # Hiển thị trạng thái barcode và tracking
                if self.enable_barcode:
                    active_tracks = len(self.tracked_barcodes)
                    cv2.putText(frame, f"Barcode: ON | Tracks: {active_tracks} | Found: {len(self.barcode_history)}", (10, 120), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                else:
                    cv2.putText(frame, "Barcode: OFF", (10, 120), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (128, 128, 128), 2)
                
                # Hiển thị trạng thái YOLO với Barcode Detection
                if self.enable_yolo:
                    yolo_status = "YOLO+ Barcode PROCESSING" if self.yolo_running else "ERROR"
                    queue_size = self.yolo_queue.qsize()
                    cv2.putText(frame, f"{yolo_status} | Queue: {queue_size}", (10, 150), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 165, 0), 2)
                else:
                    cv2.putText(frame, "YOLO+Barcode: OFF", (10, 150), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (128, 128, 128), 2)
                
                self.frame_skip_counter += 1
                
                # Hiển thị frame
                cv2.imshow('Stream', frame)
                
                # Xử lý phím bấm với timeout nhỏ
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    print("🛑 Dừng bởi người dùng")
                    break
                elif key == ord('s'):
                    # Chụp ảnh
                    filename = f"rtmp_capture_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
                    cv2.imwrite(filename, frame)
                    print(f"📸 Đã lưu ảnh: {filename}")
                elif key == ord('b'):
                    # Bật/tắt barcode detection
                    self.enable_barcode = not self.enable_barcode
                    status = "BẬT" if self.enable_barcode else "TẮT"
                    print(f"🔍 Barcode detection: {status}")
                elif key == ord('h') and self.enable_barcode:
                    # Hiển thị lịch sử barcode
                    print("\n📋 Lịch sử Barcode:")
                    print("-" * 50)
                    if self.barcode_history:
                        for i, barcode in enumerate(self.barcode_history[-10:], 1):
                            print(f"{i:2d}. [{barcode['timestamp']}] {barcode['type']}: {barcode['data']}")
                    else:
                        print("Chưa có barcode nào được phát hiện")
                    print("-" * 50)
                elif key == ord('t') and self.enable_barcode:
                    # Hiển thị thông tin tracking
                    print("\n🎯 Thông tin Tracking:")
                    print("-" * 70)
                    if self.tracked_barcodes:
                        for track_id, track in self.tracked_barcodes.items():
                            speed = np.sqrt(track['velocity'][0]**2 + track['velocity'][1]**2)
                            duration = (datetime.now() - track['created_time']).total_seconds()
                            print(f"ID {track_id:2d}: {track['type']} = {track['data'][:30]}...")
                            print(f"      Frames: {track['frame_count']} | Speed: {speed:.1f}px/f | Duration: {duration:.1f}s")
                            print(f"      Position: {track['last_position']} | Path: {len(track['path'])} points")
                    else:
                        print("Không có barcode nào đang được tracking")
                    print("-" * 70)
                elif key == ord('y'):
                    # Bật/tắt YOLO detection
                    if self.enable_yolo:
                        self.enable_yolo = False
                        self.yolo_running = False
                        print("🤖 YOLO detection: TẮT")
                    else:
                        self.enable_yolo = True
                        self.init_yolo()
                        print("🤖 YOLO detection: BẬT")
                
                # In thông tin mỗi 5 giâyp
                if frame_count % 150 == 0:  # Giả sử ~30 FPS
                    print(f"📊 Frame: {frame_count}, FPS: {fps:.1f}")
        
        except KeyboardInterrupt:
            print("\n⏹️  Dừng bởi Ctrl+C")
        
        except Exception as e:
            print(f"❌ Lỗi: {e}")
        
        finally:
            self.cleanup()
        
        return True
    
    def cleanup(self):
        """Dọn dẹp tài nguyên"""
        self.running = False
        self.yolo_running = False
        if self.cap:
            self.cap.release()
        cv2.destroyAllWindows()
        print("✅ Đã dọn dẹp tài nguyên")

def main():
    parser = argparse.ArgumentParser(description='RTMP Simple Reader with YOLO & Barcode')
    parser.add_argument('--rtmp-url', default='rtmp://127.0.0.1:1939/live/stream',
                       help='RTMP stream URL')
    parser.add_argument('--enable-barcode', action='store_true',
                       help='Enable barcode detection (may reduce FPS)')
    parser.add_argument('--enable-yolo', action='store_true',
                       help='Enable YOLO object detection (async processing)')
    
    args = parser.parse_args()
    
    print("🚀 RTMP Reader with YOLO + QR/Barcode Detection")
    print("=" * 50)
    print(f"📡 RTMP URL: {args.rtmp_url}")
    print(f"🔍 Barcode Tracking: {'BẬT' if args.enable_barcode else 'TẮT'} (phím 'b' để toggle)")
    print(f"🤖 YOLO + QR Detection: {'BẬT' if args.enable_yolo else 'TẮT'} (phím 'y' để toggle)")
    print("💡 Tính năng: YOLO detect objects → Auto scan QR/Barcode trong vùng detect")
    print("=" * 50)
    
    # Tạo reader
    reader = RTMPReader(args.rtmp_url, args.enable_barcode, args.enable_yolo)
    
    # Chạy
    try:
        success = reader.run()
        return 0 if success else 1
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
